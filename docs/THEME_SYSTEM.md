# 主题系统文档

## 概述

Cypridina 项目采用了基于 DaisyUI 的增强主题系统，支持 35+ 种不同风格的主题，包括浅色、深色、彩色和特殊效果主题。

## 主题列表

### 基础主题
- **light** (浅色) - 明亮清爽的浅色主题
- **dark** (深色) - 护眼舒适的深色主题

### 自定义主题
- **modern-blue** (现代蓝) - 现代感十足的蓝色主题
- **purple-dark** (紫色暗) - 神秘优雅的紫色暗主题
- **nature-green** (自然绿) - 清新自然的绿色主题
- **warm-orange** (温暖橙) - 温暖活力的橙色主题
- **minimal-mono** (极简) - 简约纯净的黑白主题

### DaisyUI 内置主题
- **cupcake** (纸杯蛋糕) - 甜美可爱的粉色主题
- **cyberpunk** (赛博朋克) - 未来科技感主题
- **forest** (森林) - 深邃宁静的森林主题
- **luxury** (奢华) - 高端奢华的金色主题
- **business** (商务) - 专业稳重的商务主题
- **emerald** (翡翠) - 清雅的翡翠绿主题
- **corporate** (企业) - 正式的企业级主题
- **synthwave** (合成波) - 80年代复古未来主题
- **retro** (复古) - 怀旧复古风格主题
- **valentine** (情人节) - 浪漫温馨的粉红主题
- **garden** (花园) - 花园般清新的主题
- **aqua** (水蓝) - 清澈的水蓝色主题
- **lofi** (Lo-Fi) - 舒缓放松的音乐主题
- **pastel** (粉彩) - 柔和的粉彩色主题
- **fantasy** (幻想) - 梦幻奇妙的幻想主题
- **wireframe** (线框) - 简洁的线框设计主题
- **black** (纯黑) - 极致简约的纯黑主题
- **dracula** (德古拉) - 神秘的德古拉主题
- **cmyk** (CMYK) - 印刷风格的CMYK主题
- **autumn** (秋天) - 温暖的秋日主题
- **acid** (酸性) - 鲜艳的酸性绿主题
- **lemonade** (柠檬水) - 清爽的柠檬黄主题
- **night** (夜晚) - 深邃的夜晚主题
- **coffee** (咖啡) - 温暖的咖啡色主题
- **winter** (冬天) - 清冷的冬日主题
- **dim** (昏暗) - 柔和的昏暗主题
- **nord** (北欧) - 简约的北欧风主题
- **sunset** (日落) - 温暖的日落主题

## 使用方法

### 1. 在 LiveView 中使用主题切换器

```elixir
# 在模板中添加主题切换器
<CypridinaWeb.Components.ThemeSwitcher.theme_switcher 
  current_theme={@current_theme} 
  show_label={true} 
/>

# 简化版按钮
<CypridinaWeb.Components.ThemeSwitcher.theme_toggle_button 
  current_theme={@current_theme} 
/>

# 主题预览卡片
<CypridinaWeb.Components.ThemeSwitcher.theme_preview_card 
  theme={theme} 
  current_theme={@current_theme} 
/>
```

### 2. 处理主题切换事件

```elixir
def handle_event("change_theme", %{"theme" => theme}, socket) do
  socket = 
    socket
    |> assign(:current_theme, theme)
    |> put_flash(:info, "主题已切换")
  
  {:noreply, push_event(socket, "theme-changed", %{theme: theme})}
end
```

### 3. JavaScript 集成

```javascript
// 监听主题变化
window.addEventListener('themeChanged', (event) => {
  console.log('主题已切换到:', event.detail.theme);
});

// 手动切换主题
window.themeSwitcher.setTheme('dark');

// 获取当前主题
const currentTheme = window.themeSwitcher.getCurrentTheme();
```

### 4. CSS 中使用主题变量

```css
/* 使用主题颜色 */
.my-component {
  background: oklch(var(--color-primary));
  color: oklch(var(--color-primary-content));
  border: 1px solid oklch(var(--color-base-300));
}

/* 主题特定样式 */
[data-theme="dark"] .my-component {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .my-component {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
```

## 主题系统特性

### 1. 增强的 CSS 变量系统
- 动画时长变量
- 增强的阴影系统
- 动态渐变系统
- 智能边框半径
- 高级毛玻璃效果
- 间距和字体大小系统
- Z-index 层级管理

### 2. 工具类
- 主题切换器样式
- 毛玻璃效果类
- 过渡动画类
- 悬停效果类
- 渐变文本和背景
- 响应式容器类

### 3. 动画系统
- 淡入动画 (fadeIn, fadeInUp, fadeInDown)
- 滑入动画 (slideIn, slideInRight)
- 缩放动画 (scaleIn)
- 特效动画 (pulse, glow, float, bounce)
- 交互动画 (rotate, shake, heartbeat)

### 4. 主题切换功能
- 本地存储支持
- 系统主题检测
- 键盘快捷键 (Ctrl+Shift+T)
- 主题预览功能
- 切换提示消息

## 自定义主题

### 创建新主题

1. 在 `app.css` 中添加主题定义：

```css
@plugin "../vendor/daisyui-theme" {
  name: "my-theme";
  default: false;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(98% 0 0);
  --color-primary: oklch(70% 0.2 240);
  /* 其他颜色变量... */
}
```

2. 在主题列表中添加主题信息：

```elixir
# 在 theme_switcher.ex 中添加
%{name: "my-theme", label: "我的主题", icon: "🎨", description: "自定义主题描述"}
```

### 主题变量说明

- `--color-base-100/200/300` - 背景色层级
- `--color-base-content` - 文本颜色
- `--color-primary/secondary/accent` - 主题色
- `--color-info/success/warning/error` - 状态色
- `--radius-*` - 边框半径
- `--size-*` - 尺寸变量

## 最佳实践

### 1. 主题适配
- 使用 CSS 变量而不是硬编码颜色
- 为深色主题提供特殊样式
- 确保对比度符合无障碍标准

### 2. 性能优化
- 使用 CSS 变量减少重复代码
- 利用主题切换动画提升用户体验
- 合理使用本地存储

### 3. 用户体验
- 提供主题预览功能
- 支持系统主题检测
- 添加主题切换提示

## 故障排除

### 常见问题

1. **主题不生效**
   - 检查 `data-theme` 属性是否正确设置
   - 确认主题名称在可用列表中
   - 验证 CSS 变量是否正确定义

2. **样式冲突**
   - 检查 CSS 优先级
   - 确认主题特定样式的选择器
   - 验证 Tailwind 类的覆盖

3. **JavaScript 错误**
   - 检查主题切换器是否正确初始化
   - 验证事件监听器是否正确绑定
   - 确认本地存储访问权限

### 调试工具

```javascript
// 检查当前主题
console.log(document.documentElement.getAttribute('data-theme'));

// 查看可用主题
console.log(window.themeSwitcher.getAvailableThemes());

// 监听主题变化
document.addEventListener('themeChanged', (e) => {
  console.log('主题变化:', e.detail);
});
```

## 更新日志

### v1.0.0
- 初始主题系统实现
- 支持 35+ 种主题
- 主题切换器组件
- JavaScript 集成
- 本地存储支持

### 未来计划
- 主题编辑器
- 更多动画效果
- 主题导入/导出
- 自定义颜色选择器
