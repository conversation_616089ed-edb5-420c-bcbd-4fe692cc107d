defmodule Teen.Live.GameExpectation.GameManagementBackpexTest do
  use CypridinaWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias Teen.GameManagement.ManageGameConfig

  describe "Backpex 游戏管理" do
    test "可以访问游戏管理索引页面", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查页面标题
      assert html =~ "游戏管理"
      
      # 检查 Backpex 相关元素
      assert html =~ "backpex"
    end

    test "包含正确的字段配置" do
      live_resource = Teen.Live.GameExpectation.GameManagementLive
      
      # 测试必需的回调函数
      assert live_resource.singular_name() == "游戏"
      assert live_resource.plural_name() == "游戏管理"
      
      # 测试字段配置
      fields = live_resource.fields()
      assert is_list(fields)
      
      # 检查关键字段是否存在
      field_keys = Keyword.keys(fields)
      assert :game_id in field_keys
      assert :game_name in field_keys
      assert :display_name in field_keys
      assert :status in field_keys
      assert :is_enabled in field_keys
    end

    test "包含正确的筛选器配置" do
      live_resource = Teen.Live.GameExpectation.GameManagementLive
      
      filters = live_resource.filters()
      assert is_list(filters)
      
      # 检查筛选器字段
      filter_keys = Keyword.keys(filters)
      assert :status in filter_keys
      assert :is_enabled in filter_keys
    end

    test "包含正确的操作配置" do
      live_resource = Teen.Live.GameExpectation.GameManagementLive
      
      actions = live_resource.actions()
      assert is_list(actions)
      
      # 检查操作
      action_keys = Keyword.keys(actions)
      assert :toggle_status in action_keys
      assert :manage_rooms in action_keys
    end

    test "权限验证函数正常工作" do
      live_resource = Teen.Live.GameExpectation.GameManagementLive
      
      # 测试各种权限
      assert live_resource.can?(nil, :index, nil) == true
      assert live_resource.can?(nil, :show, nil) == true
      assert live_resource.can?(nil, :new, nil) == true
      assert live_resource.can?(nil, :edit, nil) == true
      assert live_resource.can?(nil, :delete, nil) == true
      assert live_resource.can?(nil, :toggle_status, nil) == true
      assert live_resource.can?(nil, :manage_rooms, nil) == true
    end
  end

  describe "Backpex 房间管理" do
    test "可以访问房间管理索引页面", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/rooms")
      
      # 检查页面标题
      assert html =~ "房间管理"
      
      # 检查 Backpex 相关元素
      assert html =~ "backpex"
    end

    test "包含正确的字段配置" do
      live_resource = Teen.Live.GameExpectation.RoomManagementLive
      
      # 测试必需的回调函数
      assert live_resource.singular_name() == "房间"
      assert live_resource.plural_name() == "房间管理"
      
      # 测试字段配置
      fields = live_resource.fields()
      assert is_list(fields)
      
      # 检查关键字段是否存在
      field_keys = Keyword.keys(fields)
      assert :room_id in field_keys
      assert :room_name in field_keys
      assert :game_id in field_keys
      assert :level in field_keys
      assert :is_enabled in field_keys
    end

    test "包含正确的筛选器配置" do
      live_resource = Teen.Live.GameExpectation.RoomManagementLive
      
      filters = live_resource.filters()
      assert is_list(filters)
      
      # 检查筛选器字段
      filter_keys = Keyword.keys(filters)
      assert :game_id in filter_keys
      assert :is_enabled in filter_keys
      assert :level in filter_keys
    end

    test "包含正确的操作配置" do
      live_resource = Teen.Live.GameExpectation.RoomManagementLive
      
      actions = live_resource.actions()
      assert is_list(actions)
      
      # 检查操作
      action_keys = Keyword.keys(actions)
      assert :toggle_status in action_keys
    end

    test "权限验证函数正常工作" do
      live_resource = Teen.Live.GameExpectation.RoomManagementLive
      
      # 测试各种权限
      assert live_resource.can?(nil, :index, nil) == true
      assert live_resource.can?(nil, :show, nil) == true
      assert live_resource.can?(nil, :new, nil) == true
      assert live_resource.can?(nil, :edit, nil) == true
      assert live_resource.can?(nil, :delete, nil) == true
      assert live_resource.can?(nil, :toggle_status, nil) == true
    end

    test "自定义查询函数正常工作" do
      live_resource = Teen.Live.GameExpectation.RoomManagementLive
      
      # 创建一个基础查询
      base_query = Teen.GameManagement.LeveRoomConfig |> Ash.Query.new()
      
      # 应用自定义查询
      custom_query = live_resource.query(base_query, :index, %{})
      
      # 验证查询已被修改（包含排序）
      assert custom_query != base_query
    end
  end

  describe "路由集成" do
    test "游戏管理路由正确配置" do
      # 测试索引路由
      assert_raise Phoenix.Router.NoRouteError, fn ->
        get(build_conn(), "/admin/games/invalid")
      end
    end

    test "房间管理路由正确配置" do
      # 测试索引路由
      assert_raise Phoenix.Router.NoRouteError, fn ->
        get(build_conn(), "/admin/rooms/invalid")
      end
    end
  end

  describe "功能保持" do
    test "所有原有功能都通过 Backpex 实现" do
      # 游戏管理功能
      game_fields = Teen.Live.GameExpectation.GameManagementLive.fields()
      game_field_keys = Keyword.keys(game_fields)
      
      # 验证所有重要字段都存在
      required_game_fields = [:game_id, :game_name, :display_name, :description, 
                             :icon_url, :status, :is_enabled, :display_order]
      
      for field <- required_game_fields do
        assert field in game_field_keys, "游戏管理缺少字段: #{field}"
      end
      
      # 房间管理功能
      room_fields = Teen.Live.GameExpectation.RoomManagementLive.fields()
      room_field_keys = Keyword.keys(room_fields)
      
      # 验证所有重要字段都存在
      required_room_fields = [:room_id, :room_name, :game_id, :level, 
                             :min_score, :max_score, :base_score, 
                             :basic_config, :gameplay_config, :is_enabled]
      
      for field <- required_room_fields do
        assert field in room_field_keys, "房间管理缺少字段: #{field}"
      end
    end

    test "操作功能完整性" do
      # 游戏管理操作
      game_actions = Teen.Live.GameExpectation.GameManagementLive.actions()
      game_action_keys = Keyword.keys(game_actions)
      
      assert :toggle_status in game_action_keys
      assert :manage_rooms in game_action_keys
      
      # 房间管理操作
      room_actions = Teen.Live.GameExpectation.RoomManagementLive.actions()
      room_action_keys = Keyword.keys(room_actions)
      
      assert :toggle_status in room_action_keys
    end
  end
end
