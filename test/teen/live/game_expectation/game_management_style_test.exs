defmodule Teen.Live.GameExpectation.GameManagementStyleTest do
  use CypridinaWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  describe "游戏管理页面样式" do
    test "主页面包含正确的样式类", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查主容器是否有正确的ID
      assert html =~ ~s(id="game-management-container")
      
      # 检查是否包含动画类
      assert html =~ "animate-fade-in-up"
      
      # 检查是否包含主题适配的样式
      assert html =~ "text-base-content"
      assert html =~ "bg-base-100"
    end

    test "搜索工具栏包含增强样式", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查搜索工具栏样式
      assert html =~ "search-toolbar"
      assert html =~ "input-bordered"
      assert html =~ "focus:input-primary"
    end

    test "游戏卡片包含增强样式", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查游戏卡片样式
      assert html =~ "game-card"
      assert html =~ "hover-lift"
      assert html =~ "game-avatar"
    end

    test "操作下拉菜单包含增强样式", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查下拉菜单样式
      assert html =~ "action-dropdown"
      assert html =~ "dropdown-content"
    end

    test "空状态包含正确的样式", %{conn: conn} do
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 检查空状态样式（当没有游戏时）
      assert html =~ "empty-state" or html =~ "game-card"
      assert html =~ "empty-state-icon" or html =~ "game-avatar"
    end
  end

  describe "模态框样式" do
    test "模态框包含增强样式类" do
      # 模拟模态框HTML结构
      html = """
      <div class="modal modal-open modal-enhanced">
        <div class="modal-box w-11/12 max-w-2xl">
          <form class="space-y-6 form-enhanced">
            <div class="form-control">
              <input class="input input-bordered focus:input-primary" />
            </div>
          </form>
        </div>
      </div>
      """
      
      assert html =~ "modal-enhanced"
      assert html =~ "form-enhanced"
      assert html =~ "focus:input-primary"
    end
  end

  describe "主题适配" do
    test "包含主题变量的CSS类" do
      # 检查是否使用了主题变量
      css_classes = [
        "text-base-content",
        "bg-base-100",
        "bg-base-200",
        "border-base-300",
        "text-primary",
        "bg-primary",
        "text-success",
        "text-error",
        "text-warning",
        "text-info"
      ]
      
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 至少应该包含一些主题相关的类
      theme_classes_found = Enum.any?(css_classes, &String.contains?(html, &1))
      assert theme_classes_found, "页面应该包含主题相关的CSS类"
    end

    test "包含DaisyUI组件类" do
      daisyui_classes = [
        "card",
        "btn",
        "input",
        "select",
        "textarea",
        "modal",
        "dropdown",
        "badge",
        "avatar",
        "stats"
      ]
      
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 应该包含多个DaisyUI组件类
      daisyui_classes_found = Enum.count(daisyui_classes, &String.contains?(html, &1))
      assert daisyui_classes_found >= 5, "页面应该包含多个DaisyUI组件类"
    end
  end

  describe "响应式设计" do
    test "包含响应式CSS类" do
      responsive_classes = [
        "md:",
        "lg:",
        "sm:",
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-3",
        "flex-col",
        "lg:flex-row"
      ]
      
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 应该包含响应式类
      responsive_classes_found = Enum.any?(responsive_classes, &String.contains?(html, &1))
      assert responsive_classes_found, "页面应该包含响应式CSS类"
    end
  end

  describe "动画和交互" do
    test "包含动画相关的CSS类" do
      animation_classes = [
        "animate-fade-in-up",
        "transition-all",
        "hover:",
        "focus:",
        "duration-",
        "ease"
      ]
      
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 应该包含动画类
      animation_classes_found = Enum.count(animation_classes, &String.contains?(html, &1))
      assert animation_classes_found >= 3, "页面应该包含动画相关的CSS类"
    end

    test "包含交互状态类" do
      interaction_classes = [
        "hover:",
        "focus:",
        "active:",
        "disabled:",
        "cursor-pointer"
      ]
      
      {:ok, _view, html} = live(conn, ~p"/admin/games")
      
      # 应该包含交互状态类
      interaction_classes_found = Enum.any?(interaction_classes, &String.contains?(html, &1))
      assert interaction_classes_found, "页面应该包含交互状态CSS类"
    end
  end
end
