// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"
// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//
// If you have dependencies that try to import CSS, esbuild will generate a separate `app.css` file.
// To load it, simply add a second `<link>` to your `root.html.heex` file.
// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html";
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix";
import { Hooks as BackpexHooks } from "backpex";
import { LiveSocket } from "phoenix_live_view";
import topbar from "../vendor/topbar";
import { Racing<PERSON>hart<PERSON><PERSON>, WalletChartHook, MiniWallet<PERSON>hart<PERSON><PERSON>, FlashMessagesHook } from "./hooks";
import { SortableRanking } from "./sortable_ranking";
import { ChatScroll } from "./chat_scroll";
import ChatHooks from "./chat_hooks";
import MishkaComponents from "../vendor/mishka_components.js";
import ApexCharts from "apexcharts";
import ThemeSwitcher from "./theme-switcher";
// 将 ApexCharts 设置为全局变量
window.ApexCharts = ApexCharts;
const csrfToken = document
  .querySelector("meta[name='csrf-token']")
  .getAttribute("content");
// 定义Hooks
// Backpex 动态布局 Hook
const BackpexDynamicLayoutHook = {
  mounted() {
    this.adjustLayout();
    window.addEventListener('resize', () => this.adjustLayout());
  },

  updated() {
    this.adjustLayout();
  },

  adjustLayout() {
    const element = this.el;
    if (element.classList.contains('backpex-live-resource')) {
      // 确保容器使用全部可用高度
      element.style.height = '100%';
      element.style.display = 'flex';
      element.style.flexDirection = 'column';
      element.style.minHeight = '0';

      // 查找表格容器并调整
      const tableContainer = element.querySelector('.overflow-x-auto, .table-container');
      if (tableContainer) {
        tableContainer.style.flex = '1';
        tableContainer.style.display = 'flex';
        tableContainer.style.flexDirection = 'column';
        tableContainer.style.minHeight = '0';
        tableContainer.style.overflow = 'hidden';

        const table = tableContainer.querySelector('.table');
        if (table) {
          table.style.flex = '1';
          table.style.overflow = 'auto';
          table.style.minHeight = '0';
        }
      }
    }
  }
};

// 增强的主题切换 Hook
const ThemeHook = {
  mounted() {
    // 监听主题变化事件
    this.handleEvent("theme-changed", ({ theme }) => {
      this.applyTheme(theme);
      this.storeTheme(theme);
      this.showThemeToast(theme);
    });

    this.handleEvent("preview-theme", ({ theme }) => {
      this.applyTheme(theme);
    });

    // 初始化主题
    const storedTheme = this.getStoredTheme() || this.detectSystemTheme();
    if (storedTheme) {
      this.applyTheme(storedTheme);
    }

    // 监听系统主题变化
    this.watchSystemTheme();

    // 键盘快捷键支持
    this.bindKeyboardShortcuts();
  },

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);

    // 添加主题切换动画
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';

    // 更新主题显示
    this.updateThemeDisplay(theme);

    // 通知其他组件主题已变化
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme }
    }));
  },

  getStoredTheme() {
    return localStorage.getItem('theme');
  },

  storeTheme(theme) {
    localStorage.setItem('theme', theme);
  },

  detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  },

  watchSystemTheme() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        if (!this.getStoredTheme()) {
          const systemTheme = e.matches ? 'dark' : 'light';
          this.applyTheme(systemTheme);
        }
      });
    }
  },

  bindKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  },

  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
    this.storeTheme(newTheme);
    this.showThemeToast(newTheme);
  },

  updateThemeDisplay(theme) {
    const themeNames = {
      'light': '浅色',
      'dark': '深色',
      'modern-blue': '现代蓝',
      'purple-dark': '紫色暗',
      'nature-green': '自然绿',
      'warm-orange': '温暖橙',
      'cyberpunk': '赛博朋克',
      'forest': '森林',
      'luxury': '奢华',
      'business': '商务',
      'synthwave': '合成波',
      'dracula': '德古拉',
      'coffee': '咖啡',
      'nord': '北欧'
    };

    const display = document.getElementById('current-theme-display');
    if (display) {
      display.textContent = `当前: ${themeNames[theme] || theme}`;
    }
  },

  showThemeToast(theme) {
    const themeNames = {
      'light': '浅色',
      'dark': '深色',
      'modern-blue': '现代蓝',
      'purple-dark': '紫色暗',
      'nature-green': '自然绿',
      'warm-orange': '温暖橙',
      'cyberpunk': '赛博朋克',
      'forest': '森林',
      'luxury': '奢华',
      'business': '商务',
      'synthwave': '合成波',
      'dracula': '德古拉',
      'coffee': '咖啡',
      'nord': '北欧'
    };

    // 移除现有的提示
    const existingToast = document.querySelector('.theme-toast');
    if (existingToast) {
      existingToast.remove();
    }

    // 创建新提示
    const toast = document.createElement('div');
    toast.className = 'theme-toast toast toast-top toast-end z-50';
    toast.innerHTML = `
      <div class="alert alert-info animate-fade-in-down">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
        </svg>
        <span>已切换到 ${themeNames[theme] || theme} 主题</span>
      </div>
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
      toast.remove();
    }, 3000);
  }
};

// 主题预览 Hook
const ThemePreviewHook = {
  mounted() {
    this.originalTheme = document.documentElement.getAttribute('data-theme');

    this.el.addEventListener('mouseenter', () => {
      const theme = this.el.dataset.theme;
      if (theme) {
        document.documentElement.setAttribute('data-theme', theme);
      }
    });

    this.el.addEventListener('mouseleave', () => {
      document.documentElement.setAttribute('data-theme', this.originalTheme);
    });
  },

  updated() {
    this.originalTheme = document.documentElement.getAttribute('data-theme');
  }
};

const Hooks = {
  RacingChart: RacingChartHook,
  WalletChart: WalletChartHook,
  MiniWalletChart: MiniWalletChartHook,
  FlashMessages: FlashMessagesHook,
  SortableRanking: SortableRanking,
  ChatScroll: ChatScroll,
  BackpexDynamicLayout: BackpexDynamicLayoutHook,
  Theme: ThemeHook,
  ThemePreview: ThemePreviewHook,
  // 通用聊天系统hooks
  ...ChatHooks,
};
const liveSocket = new LiveSocket("/live", Socket, {
  // longPollFallbackMs: 2500,
  params: {
    _csrf_token: csrfToken,
  },
  hooks: {
    ...Hooks,
    ...BackpexHooks,
    ...MishkaComponents,
  },
});
BackpexHooks.BackpexThemeSelector.setStoredTheme();
// Show progress bar on live navigation and form submits
topbar.config({
  barColors: {
    0: "#29d",
  },
  shadowColor: "rgba(0, 0, 0, .3)",
});
window.addEventListener("phx:page-loading-start", (_info) => topbar.show(300));
window.addEventListener("phx:page-loading-stop", (_info) => topbar.hide());
// connect if there are any LiveViews on the page
liveSocket.connect();
// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket;
// The lines below enable quality of life phoenix_live_reload
// development features:
//
//     1. stream server logs to the browser console
//     2. click on elements to jump to their definitions in your code editor
//
if (process.env.NODE_ENV === "development") {
  window.addEventListener(
    "phx:live_reload:attached",
    ({ detail: reloader }) => {
      // Enable server log streaming to client.
      // Disable with reloader.disableServerLogs()
      reloader.enableServerLogs();
      // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
      //
      //   * click with "c" key pressed to open at caller location
      //   * click with "d" key pressed to open at function component definition location
      let keyDown;
      window.addEventListener("keydown", (e) => (keyDown = e.key));
      window.addEventListener("keyup", () => (keyDown = null));
      window.addEventListener(
        "click",
        (e) => {
          if (keyDown === "c") {
            e.preventDefault();
            e.stopImmediatePropagation();
            reloader.openEditorAtCaller(e.target);
          } else if (keyDown === "d") {
            e.preventDefault();
            e.stopImmediatePropagation();
            reloader.openEditorAtDef(e.target);
          }
        },
        true,
      );
      window.liveReloader = reloader;
    },
  );
}
