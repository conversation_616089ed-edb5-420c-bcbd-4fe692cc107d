defmodule Teen.Live.GameExpectation.GameManagementLive do
  @moduledoc """
  游戏管理后台界面
  提供游戏配置的增删改查、状态管理等功能
  """
  use CypridinaWeb, :live_view
  require Ash.Query
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "游戏管理")
     |> assign(:current_url, "/admin/games")
     |> assign(:search_term, "")
     |> assign(:selected_status, "all")
     |> assign(:show_modal, false)
     |> assign(:modal_action, nil)
     |> assign(:current_game, nil)
     |> assign(:fluid?, true)
     |> load_games(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "游戏管理")
    |> assign(:current_game, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "新建游戏")
    |> assign(:current_game, %ManageGameConfig{})
    |> assign(:show_modal, true)
    |> assign(:modal_action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    game = ManageGameConfig.get_by_id!(id)

    socket
    |> assign(:page_title, "编辑游戏")
    |> assign(:current_game, game)
    |> assign(:show_modal, true)
    |> assign(:modal_action, :edit)
  end

  defp apply_action(socket, :new_room, %{"id" => game_id}) do
    game = ManageGameConfig.get_by_id!(game_id)

    socket
    |> assign(:page_title, "#{game.game_name} - 新建房间")
    |> assign(:current_game, game)
    |> assign(:current_room, %LeveRoomConfig{game_id: game.game_id})
    |> assign(:show_room_modal, true)
    |> assign(:room_modal_action, :new)
    |> assign(:view_mode, :rooms)
    |> load_rooms()
  end

  defp apply_action(socket, :edit_room, %{"id" => game_id, "room_id" => room_id}) do
    game = ManageGameConfig.get_by_id!(game_id)
    room = LeveRoomConfig.get_by_id!(room_id) |> Ash.load!(:game_config)

    socket
    |> assign(:page_title, "#{game.game_name} - 编辑房间")
    |> assign(:current_game, game)
    |> assign(:current_room, room)
    |> assign(:show_room_modal, true)
    |> assign(:room_modal_action, :edit)
    |> assign(:view_mode, :rooms)
    |> load_rooms()
  end

  defp apply_action(socket, :rooms, %{"id" => id}) do
    game = ManageGameConfig.get_by_id!(id)

    socket
    |> assign(:page_title, "房间配置 - #{game.display_name}")
    |> assign(:current_game, game)
    |> assign(:view_mode, :rooms)
    |> assign(:show_room_modal, false)
    |> assign(:room_modal_action, nil)
    |> assign(:current_room, nil)
    |> load_rooms()
  end

  @impl true
  def handle_event("search", %{"search" => %{"term" => term}}, socket) do
    {:noreply,
     socket
     |> assign(:search_term, term)
     |> load_games()}
  end

  def handle_event("filter_status", %{"status" => status}, socket) do
    {:noreply,
     socket
     |> assign(:selected_status, status)
     |> load_games()}
  end

  def handle_event("toggle_status", %{"id" => id}, socket) do
    game = ManageGameConfig.get_by_id!(id)

    result =
      if game.is_enabled do
        ManageGameConfig.disable!(game, %{}, actor: get_current_user_id(socket))
      else
        ManageGameConfig.enable!(game, %{}, actor: get_current_user_id(socket))
      end

    case result do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "游戏状态已更新")
         |> load_games()}

      {:error, error} ->
        require Logger
        Logger.error("游戏状态更新失败: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "更新失败: #{inspect(error)}")}
    end
  end

  def handle_event("delete_game", %{"id" => id}, socket) do
    game = ManageGameConfig.get_by_id!(id: id)

    case ManageGameConfig.destroy!(game) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "游戏已删除")
         |> load_games()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "删除失败")}
    end
  end

  def handle_event("close_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:current_game, nil)
     |> assign(:modal_action, nil)}
  end

  def handle_event("save_game", %{"game_config" => game_params}, socket) do
    case socket.assigns.modal_action do
      :new ->
        game_params = Map.put(game_params, "created_by", get_current_user_id(socket))

        case ManageGameConfig.create(game_params) do
          {:ok, _game} ->
            {:noreply,
             socket
             |> put_flash(:info, "游戏创建成功")
             |> assign(:show_modal, false)
             |> load_games()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建失败: #{inspect(changeset.errors)}")}
        end

      :edit ->
        game_params = Map.put(game_params, "updated_by", get_current_user_id(socket))

        case ManageGameConfig.update(socket.assigns.current_game, game_params) do
          {:ok, updated_game} ->
            {:noreply,
             socket
             |> put_flash(:info, "游戏更新成功")
             |> assign(:show_modal, false)
             |> load_games()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新失败: #{inspect(changeset.errors)}")}
        end
    end
  end

  # 房间管理相关事件处理
  def handle_event("close_room_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_room_modal, false)
     |> assign(:current_room, nil)
     |> assign(:room_modal_action, nil)}
  end

  def handle_event("save_room", %{"room_config" => room_params}, socket) do
    # 处理JSON配置
    room_params = process_json_configs(room_params)

    case socket.assigns.room_modal_action do
      :new ->
        room_params =
          room_params
          |> Map.put("game_id", socket.assigns.current_game.game_id)
          |> Map.put("created_by", get_current_user_id(socket))

        case LeveRoomConfig.create(room_params) do
          {:ok, _room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间创建成功")
             |> assign(:show_room_modal, false)
             |> load_rooms()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建失败: #{format_changeset_errors(changeset)}")}
        end

      :edit ->
        room_params = Map.put(room_params, "updated_by", get_current_user_id(socket))

        case LeveRoomConfig.update(socket.assigns.current_room, room_params) do
          {:ok, updated_room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间更新成功")
             |> assign(:show_room_modal, false)
             |> load_rooms()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新失败: #{format_changeset_errors(changeset)}")}
        end
    end
  end

  def handle_event("toggle_room_status", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id)

    result =
      if room.is_enabled do
        LeveRoomConfig.disable!(room, %{}, actor: get_current_user_id(socket))
      else
        LeveRoomConfig.enable!(room, %{}, actor: get_current_user_id(socket))
      end

    case result do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "房间状态已更新")
         |> load_rooms()}

      {:error, error} ->
        require Logger
        Logger.error("房间状态更新失败: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "更新失败: #{inspect(error)}")}
    end
  end

  def handle_event("delete_room", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id)

    case LeveRoomConfig.destroy!(room) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "房间已删除")
         |> load_rooms()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "删除失败")}
    end
  end

  defp load_games(socket) do
    games =
      try do
        # 在后台管理界面中读取所有游戏配置（包括禁用的）
        ManageGameConfig
        |> Ash.read!()
        |> filter_by_search(socket.assigns.search_term)
        |> filter_by_status(socket.assigns.selected_status)
        |> Enum.sort_by(& &1.display_order)
      rescue
        error ->
          # 如果读取失败，返回空列表并记录错误
          require Logger
          Logger.error("加载游戏配置失败: #{inspect(error)}")
          []
      end

    assign(socket, :games, games)
  end

  defp load_rooms(socket) do
    rooms =
      try do
        case socket.assigns.current_game do
          nil ->
            []

          current_game ->
            LeveRoomConfig
            |> Ash.Query.filter(game_id: current_game.game_id)
            |> Ash.read!()
            |> Enum.sort_by(& &1.server_id)
        end
      rescue
        error ->
          require Logger
          Logger.error("加载房间配置失败: #{inspect(error)}")
          []
      end

    assign(socket, :rooms, rooms)
  end

  defp filter_by_search(games, ""), do: games

  defp filter_by_search(games, term) do
    term = String.downcase(term)

    Enum.filter(games, fn game ->
      String.contains?(String.downcase(game.game_name), term) or
        String.contains?(String.downcase(game.display_name), term)
    end)
  end

  defp filter_by_status(games, "all"), do: games
  defp filter_by_status(games, "enabled"), do: Enum.filter(games, & &1.is_enabled)
  defp filter_by_status(games, "disabled"), do: Enum.filter(games, &(not &1.is_enabled))

  defp filter_by_status(games, status) when status in ["0", "1", "2", "4"] do
    status_int = String.to_integer(status)
    Enum.filter(games, &(&1.status == status_int))
  end

  defp get_current_user_id(socket) do
    # 从session或assigns中获取当前用户ID
    # 这里需要根据实际的认证系统来实现
    # 如果没有当前用户，使用系统默认UUID
    Map.get(socket.assigns, :current_user_id, "00000000-0000-0000-0000-000000000000")
  end

  defp process_json_configs(room_params) do
    # 新的简单编辑器直接传递配置数据，不需要JSON解析
    room_params
  end

  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end

  defp status_badge(status) do
    case status do
      0 -> {"停用", "bg-red-100 text-red-800"}
      1 -> {"维护", "bg-yellow-100 text-yellow-800"}
      2 -> {"正常", "bg-green-100 text-green-800"}
      4 -> {"即将开放", "bg-blue-100 text-blue-800"}
      _ -> {"未知", "bg-gray-100 text-gray-800"}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="p-6">
      <%= if assigns[:view_mode] do %>
        {render_sub_view(assigns)}
      <% else %>
        {render_main_view(assigns)}
      <% end %>
    </div>
    """
  end

  defp render_sub_view(assigns) do
    case assigns.view_mode do
      :rooms -> render_rooms_view(assigns)
      _ -> ~H"<div>
  <p>未知视图模式</p>
</div>"
    end
  end

  defp render_rooms_view(assigns) do
    ~H"""
    <div>
      <!-- 面包屑导航 -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <.link
              navigate={~p"/admin/games"}
              class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              游戏管理
            </.link>
          </li>
          <li>
            <div class="flex items-center">
              <svg
                class="w-3 h-3 text-gray-400 mx-1"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 6 10"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m1 9 4-4-4-4"
                />
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                {@current_game.display_name} - 房间配置
              </span>
            </div>
          </li>
        </ol>
      </nav>

    <!-- 页面标题和操作 -->
      <div class="mb-6 flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{@current_game.display_name} - 房间配置</h1>
          <p class="mt-1 text-sm text-gray-600">管理游戏的房间分场配置</p>
        </div>
        <.link
          patch={~p"/admin/games/#{@current_game.id}/rooms/new"}
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
        >
          新建房间
        </.link>
      </div>

    <!-- 房间列表 -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <%= if @rooms && length(@rooms) > 0 do %>
          <ul role="list" class="divide-y divide-gray-200">
            <%= for room <- @rooms do %>
              <li class="px-6 py-4">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-lg bg-indigo-100 flex items-center justify-center">
                          <span class="text-sm font-medium text-indigo-700">{room.server_id}</span>
                        </div>
                      </div>
                      <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                          <div>
                            <p class="text-sm font-medium text-gray-900">房间 {room.server_id}</p>
                            <p class="text-sm text-gray-500">
                              最小下注: {room.min_bet} | 入场费: {room.entry_fee} | 最大人数: {room.max_players}
                            </p>
                          </div>
                          <div class="flex items-center space-x-2">
                            <%= if room.is_enabled do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                启用
                              </span>
                            <% else %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                禁用
                              </span>
                            <% end %>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2 ml-4">
                    <.link
                      patch={~p"/admin/games/#{@current_game.id}/rooms/#{room.id}/edit"}
                      class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                    >
                      编辑
                    </.link>
                    <button
                      phx-click="toggle_room_status"
                      phx-value-id={room.id}
                      class="text-blue-600 hover:text-blue-900 text-sm font-medium"
                    >
                      {if room.is_enabled, do: "禁用", else: "启用"}
                    </button>
                    <button
                      phx-click="delete_room"
                      phx-value-id={room.id}
                      data-confirm="确定要删除这个房间吗？"
                      class="text-red-600 hover:text-red-900 text-sm font-medium"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        <% else %>
          <div class="text-center py-12">
            <svg
              class="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无房间配置</h3>
            <p class="mt-1 text-sm text-gray-500">开始为这个游戏创建房间分场配置</p>
            <div class="mt-6">
              <.link
                patch={~p"/admin/games/#{@current_game.id}/rooms/new"}
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                新建房间
              </.link>
            </div>
          </div>
        <% end %>
      </div>

    <!-- 房间配置模态框 -->
      <%= if assigns[:show_room_modal] do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                {if @room_modal_action == :new, do: "新建房间", else: "编辑房间"}
              </h3>

              <.form for={%{}} phx-submit="save_room" class="space-y-6">
                <!-- 基础配置 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">服务器ID</label>
                    <input
                      type="number"
                      name="room_config[server_id]"
                      value={@current_room && @current_room.server_id}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

    <!-- 最小下注字段已隐藏 -->
                  <input
                    type="hidden"
                    name="room_config[min_bet]"
                    value={(@current_room && @current_room.min_bet) || 100}
                  />

                  <div>
                    <label class="block text-sm font-medium text-gray-700">入场费</label>
                    <input
                      type="number"
                      name="room_config[entry_fee]"
                      value={@current_room && @current_room.entry_fee}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

    <!-- 最大玩家数字段已隐藏 -->
                  <input
                    type="hidden"
                    name="room_config[max_players]"
                    value={(@current_room && @current_room.max_players) || 6}
                  />

                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      name="room_config[is_enabled]"
                      checked={@current_room && @current_room.is_enabled}
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label class="ml-2 block text-sm text-gray-900">启用</label>
                  </div>
                </div>

    <!-- 统一配置 -->
                <.live_component
                  module={CypridinaWeb.Components.SimpleConfigEditor}
                  id="unified_config_editor"
                  title="游戏配置"
                  description="完整的游戏配置，包括房间设置、投注限额、RTP、赔率表、Jackpot等所有参数"
                  field_name="room_config[unified_config]"
                  config_type={:unified_config}
                  room={@current_room}
                  game_id={@current_game && @current_game.id}
                  room_id={@current_room && @current_room.id}
                />

                <div class="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    phx-click="close_room_modal"
                    class="px-4 py-2 text-sm font-medium text-gray-800 bg-gray-300 rounded-md border border-gray-400 hover:bg-gray-400 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-700 rounded-md border border-blue-600 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  >
                    保存
                  </button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_main_view(assigns) do
    ~H"""
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-base-content animate-fade-in-up">
            <.icon name="hero-puzzle-piece" class="inline-block w-8 h-8 mr-3 text-primary" />
            游戏管理
          </h1>
          <p class="mt-2 text-base-content/70 animate-fade-in-up" style="animation-delay: 0.1s;">
            管理游戏配置和房间设置，控制游戏状态和参数
          </p>
        </div>
        <div class="hidden md:flex items-center space-x-4">
          <div class="stats shadow bg-base-200">
            <div class="stat">
              <div class="stat-title">总游戏数</div>
              <div class="stat-value text-primary"><%= length(@games) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">已启用</div>
              <div class="stat-value text-success">
                <%= Enum.count(@games, & &1.is_enabled) %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选工具栏 -->
    <div class="card bg-base-100 shadow-lg mb-6 animate-fade-in-up search-toolbar" style="animation-delay: 0.2s;">
      <div class="card-body">
        <div class="flex flex-col lg:flex-row gap-4 items-center">
          <!-- 搜索框 -->
          <div class="flex-1 w-full lg:w-auto">
            <.form for={%{}} phx-submit="search" class="join w-full">
              <input
                type="text"
                name="search[term]"
                value={@search_term}
                placeholder="🔍 搜索游戏名称或ID..."
                class="input input-bordered join-item flex-1 focus:input-primary transition-all"
              />
              <button type="submit" class="btn btn-primary join-item">
                <.icon name="hero-magnifying-glass" class="w-4 h-4" />
                搜索
              </button>
            </.form>
          </div>

          <!-- 状态筛选 -->
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-base-content/70">状态筛选:</span>
            <select
              phx-change="filter_status"
              name="status"
              class="select select-bordered select-sm focus:select-primary transition-all"
            >
              <option value="all" selected={@selected_status == "all"}>🌐 全部状态</option>
              <option value="enabled" selected={@selected_status == "enabled"}>✅ 已启用</option>
              <option value="disabled" selected={@selected_status == "disabled"}>❌ 已禁用</option>
              <option value="2" selected={@selected_status == "2"}>🟢 正常运行</option>
              <option value="1" selected={@selected_status == "1"}>🟡 维护中</option>
              <option value="0" selected={@selected_status == "0"}>🔴 已停用</option>
              <option value="4" selected={@selected_status == "4"}>🔵 即将开放</option>
            </select>
          </div>

          <!-- 新建按钮 -->
          <.link
            patch={~p"/admin/games/new"}
            class="btn btn-primary btn-sm gap-2 hover:btn-primary-focus transition-all hover-lift"
          >
            <.icon name="hero-plus" class="w-4 h-4" />
            新建游戏
          </.link>
        </div>
      </div>
    </div>

    <!-- 游戏列表 -->
    <%= if length(@games) > 0 do %>
      <div class="grid grid-cols-1 gap-4 animate-fade-in-up" style="animation-delay: 0.3s;">
        <%= for {game, index} <- Enum.with_index(@games) do %>
          <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift game-card"
               style={"animation-delay: #{0.4 + index * 0.05}s;"}>
            <div class="card-body">
              <div class="flex items-center justify-between">
                <!-- 游戏信息 -->
                <div class="flex items-center space-x-4">
                  <!-- 游戏图标 -->
                  <div class="avatar">
                    <div class="w-16 h-16 rounded-xl game-avatar">
                      <%= if game.icon_url do %>
                        <img src={game.icon_url} alt={game.display_name} class="rounded-xl" />
                      <% else %>
                        <div class="w-16 h-16 rounded-xl bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                          <span class="text-xl font-bold text-primary-content">
                            <%= String.first(game.display_name) %>
                          </span>
                        </div>
                      <% end %>
                    </div>
                  </div>
                  <!-- 游戏详情 -->
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-2">
                      <h3 class="text-lg font-semibold text-base-content"><%= game.display_name %></h3>

                      <!-- 状态徽章 -->
                      <div class="badge badge-lg <%= case game.status do
                        2 -> "badge-success"
                        1 -> "badge-warning"
                        0 -> "badge-error"
                        4 -> "badge-info"
                        _ -> "badge-neutral"
                      end %>">
                        <%= elem(status_badge(game.status), 0) %>
                      </div>

                      <!-- 启用状态 -->
                      <%= if game.is_enabled do %>
                        <div class="badge badge-success badge-outline">
                          <.icon name="hero-check-circle" class="w-3 h-3 mr-1" />
                          启用
                        </div>
                      <% else %>
                        <div class="badge badge-error badge-outline">
                          <.icon name="hero-x-circle" class="w-3 h-3 mr-1" />
                          禁用
                        </div>
                      <% end %>
                    </div>

                    <div class="text-sm text-base-content/70 space-y-1">
                      <p class="flex items-center gap-2">
                        <.icon name="hero-hashtag" class="w-4 h-4" />
                        ID: <span class="font-mono"><%= game.game_id %></span>
                      </p>
                      <p class="flex items-center gap-2">
                        <.icon name="hero-code-bracket" class="w-4 h-4" />
                        名称: <span class="font-mono"><%= game.game_name %></span>
                      </p>
                      <%= if game.description do %>
                        <p class="flex items-start gap-2 mt-2">
                          <.icon name="hero-document-text" class="w-4 h-4 mt-0.5" />
                          <span><%= game.description %></span>
                        </p>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>

                <!-- 操作按钮 -->
                <div class="flex items-center gap-2">
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                      <.icon name="hero-ellipsis-vertical" class="w-4 h-4" />
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-52 action-dropdown">
                      <li>
                        <.link
                          patch={~p"/admin/games/#{game.id}/rooms"}
                          class="flex items-center gap-2 hover:bg-primary hover:text-primary-content"
                        >
                          <.icon name="hero-building-office" class="w-4 h-4" />
                          房间配置
                        </.link>
                      </li>
                      <li>
                        <.link
                          patch={~p"/admin/games/#{game.id}/edit"}
                          class="flex items-center gap-2 hover:bg-info hover:text-info-content"
                        >
                          <.icon name="hero-pencil-square" class="w-4 h-4" />
                          编辑游戏
                        </.link>
                      </li>
                      <li>
                        <button
                          phx-click="toggle_status"
                          phx-value-id={game.id}
                          class={"flex items-center gap-2 w-full text-left #{if game.is_enabled, do: "hover:bg-warning hover:text-warning-content", else: "hover:bg-success hover:text-success-content"}"}
                        >
                          <%= if game.is_enabled do %>
                            <.icon name="hero-pause" class="w-4 h-4" />
                            禁用游戏
                          <% else %>
                            <.icon name="hero-play" class="w-4 h-4" />
                            启用游戏
                          <% end %>
                        </button>
                      </li>
                      <div class="divider my-1"></div>
                      <li>
                        <button
                          phx-click="delete_game"
                          phx-value-id={game.id}
                          data-confirm="确定要删除这个游戏吗？此操作不可撤销！"
                          class="flex items-center gap-2 w-full text-left hover:bg-error hover:text-error-content"
                        >
                          <.icon name="hero-trash" class="w-4 h-4" />
                          删除游戏
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- 空状态 -->
      <div class="card bg-base-100 shadow-lg animate-fade-in-up empty-state" style="animation-delay: 0.3s;">
        <div class="card-body text-center py-16">
          <div class="text-6xl mb-4 empty-state-icon">🎮</div>
          <h3 class="text-xl font-semibold text-base-content mb-2">暂无游戏</h3>
          <p class="text-base-content/70 mb-6">还没有创建任何游戏，点击下方按钮开始创建第一个游戏</p>
          <.link
            patch={~p"/admin/games/new"}
            class="btn btn-primary gap-2"
          >
            <.icon name="hero-plus" class="w-4 h-4" />
            创建第一个游戏
          </.link>
        </div>
      </div>
    <% end %>

    <!-- 模态框 -->
    <%= if @show_modal do %>
      <div class="modal modal-open modal-enhanced">
        <div class="modal-box w-11/12 max-w-2xl">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-base-content flex items-center gap-3">
              <%= if @modal_action == :new do %>
                <.icon name="hero-plus-circle" class="w-8 h-8 text-primary" />
                新建游戏
              <% else %>
                <.icon name="hero-pencil-square" class="w-8 h-8 text-info" />
                编辑游戏
              <% end %>
            </h3>
            <button
              phx-click="close_modal"
              class="btn btn-sm btn-circle btn-ghost hover:btn-error"
            >
              <.icon name="hero-x-mark" class="w-4 h-4" />
            </button>
          </div>

          <.form for={%{}} phx-submit="save_game" class="space-y-6 form-enhanced">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 游戏ID -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium flex items-center gap-2">
                    <.icon name="hero-hashtag" class="w-4 h-4" />
                    游戏ID
                  </span>
                </label>
                <input
                  type="number"
                  name="game_config[game_id]"
                  value={@current_game && @current_game.game_id}
                  required
                  class="input input-bordered focus:input-primary"
                />
                <label class="label">
                  <span class="label-text-alt text-base-content/60">唯一标识符，用于系统内部识别</span>
                </label>
              </div>

              <!-- 游戏名称 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium flex items-center gap-2">
                    <.icon name="hero-code-bracket" class="w-4 h-4" />
                    游戏名称
                  </span>
                </label>
                <input
                  type="text"
                  name="game_config[game_name]"
                  value={@current_game && @current_game.game_name}
                  required
                  class="input input-bordered focus:input-primary"
                />
                <label class="label">
                  <span class="label-text-alt text-base-content/60">系统内部使用的游戏名称</span>
                </label>
              </div>

              <!-- 显示名称 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium flex items-center gap-2">
                    <.icon name="hero-eye" class="w-4 h-4" />
                    显示名称
                  </span>
                </label>
                <input
                  type="text"
                  name="game_config[display_name]"
                  value={@current_game && @current_game.display_name}
                  required
                  class="input input-bordered focus:input-primary"
                />
                <label class="label">
                  <span class="label-text-alt text-base-content/60">用户看到的游戏名称</span>
                </label>
              </div>

              <!-- 状态 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium flex items-center gap-2">
                    <.icon name="hero-signal" class="w-4 h-4" />
                    运行状态
                  </span>
                </label>
                <select
                  name="game_config[status]"
                  class="select select-bordered focus:select-primary"
                >
                  <option value="2" selected={@current_game && @current_game.status == 2}>
                    🟢 正常运行
                  </option>
                  <option value="1" selected={@current_game && @current_game.status == 1}>
                    🟡 维护中
                  </option>
                  <option value="0" selected={@current_game && @current_game.status == 0}>
                    🔴 已停用
                  </option>
                  <option value="4" selected={@current_game && @current_game.status == 4}>
                    🔵 即将开放
                  </option>
                </select>
              </div>
            </div>

            <!-- 图标URL -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium flex items-center gap-2">
                  <.icon name="hero-photo" class="w-4 h-4" />
                  图标URL
                </span>
              </label>
              <input
                type="text"
                name="game_config[icon_url]"
                value={@current_game && @current_game.icon_url}
                class="input input-bordered focus:input-primary"
                placeholder="https://example.com/icon.png"
              />
              <label class="label">
                <span class="label-text-alt text-base-content/60">游戏图标的网络地址（可选）</span>
              </label>
            </div>

            <!-- 描述 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium flex items-center gap-2">
                  <.icon name="hero-document-text" class="w-4 h-4" />
                  游戏描述
                </span>
              </label>
              <textarea
                name="game_config[description]"
                rows="3"
                class="textarea textarea-bordered focus:textarea-primary"
                placeholder="输入游戏的详细描述..."
              ><%= @current_game && @current_game.description %></textarea>
              <label class="label">
                <span class="label-text-alt text-base-content/60">游戏的详细说明（可选）</span>
              </label>
            </div>

            <!-- 显示顺序 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium flex items-center gap-2">
                  <.icon name="hero-bars-3-bottom-left" class="w-4 h-4" />
                  显示顺序
                </span>
              </label>
              <input
                type="number"
                name="game_config[display_order]"
                value={@current_game && @current_game.display_order}
                class="input input-bordered focus:input-primary"
                placeholder="0"
              />
              <label class="label">
                <span class="label-text-alt text-base-content/60">数字越小排序越靠前</span>
              </label>
            </div>

            <!-- 启用状态 -->
            <div class="form-control">
              <label class="label cursor-pointer justify-start gap-4">
                <input
                  type="checkbox"
                  name="game_config[is_enabled]"
                  checked={@current_game && @current_game.is_enabled}
                  class="checkbox checkbox-primary"
                />
                <div>
                  <span class="label-text font-medium flex items-center gap-2">
                    <.icon name="hero-power" class="w-4 h-4" />
                    启用游戏
                  </span>
                  <div class="label-text-alt text-base-content/60">启用后用户可以访问此游戏</div>
                </div>
              </label>
            </div>

            <!-- 操作按钮 -->
            <div class="modal-action">
              <button
                type="button"
                phx-click="close_modal"
                class="btn btn-ghost gap-2"
              >
                <.icon name="hero-x-mark" class="w-4 h-4" />
                取消
              </button>
              <button
                type="submit"
                class="btn btn-primary gap-2"
              >
                <.icon name="hero-check" class="w-4 h-4" />
                <%= if @modal_action == :new, do: "创建游戏", else: "保存更改" %>
              </button>
            </div>
          </.form>
        </div>
      </div>
    <% end %>
    """
  end

  # 获取默认基础配置
  defp get_default_basic_config() do
    %{
      "max_players" => 1,
      "min_players" => 1,
      "auto_start_delay" => 1000,
      "difen" => 100,
      "bet_rate_num" => 9,
      "score_rate" => 1,
      "odds_config" => %{
        "1" => 0.2,
        "2" => 1,
        "3" => 2,
        "4" => 10,
        "5" => 20,
        "6" => 100,
        "7" => 200
      }
    }
  end

  # 获取默认玩法配置
  defp get_default_gameplay_config() do
    %{
      "rtp" => 85.0,
      "icon_weights" => %{
        # WILD字
        "0" => 1,
        # 香蕉
        "1" => 30,
        # 西瓜
        "2" => 25,
        # 草莓
        "3" => 20,
        # 葡萄
        "4" => 15,
        # 芒果
        "5" => 12,
        # 榴莲
        "6" => 10,
        # 山竹
        "7" => 8,
        # BAR
        "8" => 3,
        # 苹果
        "9" => 4,
        # 7
        "10" => 2
      },
      "big_win_control" => %{
        "jackpot_probability" => 0.01,
        "free_game_probability" => 0.05,
        "big_win_cooldown" => 100
      },
      "payout_table" => %{
        "0" => %{},
        "1" => %{"2" => 1, "3" => 3, "4" => 10, "5" => 75},
        "2" => %{"3" => 3, "4" => 10, "5" => 85},
        "3" => %{"3" => 15, "4" => 40, "5" => 250},
        "4" => %{"3" => 25, "4" => 50, "5" => 400},
        "5" => %{"3" => 30, "4" => 70, "5" => 550},
        "6" => %{"3" => 35, "4" => 80, "5" => 650},
        "7" => %{"3" => 45, "4" => 100, "5" => 800},
        "8" => %{"3" => 75, "4" => 175, "5" => 1250},
        "9" => %{"3" => 25, "4" => 40, "5" => 400},
        "10" => %{"3" => 100, "4" => 200, "5" => 1750}
      },
      "jackpot_seed_amount" => 10000,
      "jackpot_contribution_rate" => 0.005,
      "jackpot_min_trigger_amount" => 50000,
      "jackpot_percentage_table" => %{
        "200" => %{"5" => 30, "4" => 25, "3" => 20},
        "100" => %{"5" => 20, "4" => 15, "3" => 12},
        "20" => %{"5" => 12, "4" => 6, "3" => 4},
        "10" => %{"5" => 8, "4" => 4, "3" => 2}
      },
      "free_game_trigger_table" => %{
        "5" => 12,
        "4" => 8,
        "3" => 4
      },
      "free_game_multiplier" => 1.5,
      "enable_free_game_test" => false,
      "free_game_test_spins" => 2,
      "enable_jackpot_test" => false
    }
  end

  # 从房间配置获取游戏类型
  defp get_game_type_from_room(current_room) do
    cond do
      current_room && current_room.game_id == 40 -> "slot777"
      current_room && current_room.game_id == 41 -> "slotniu"
      current_room && current_room.game_id == 42 -> "slotcat"
      current_room && current_room.game_id == 1 -> "teenpatti"
      current_room && current_room.game_id == 22 -> "longhu"
      true -> "unknown"
    end
  end

  # 从房间获取游戏名称
  defp get_game_name_from_room(current_room) do
    cond do
      current_room && current_room.game_config && current_room.game_config.name ->
        current_room.game_config.name

      current_room && Map.has_key?(current_room, :game_id) ->
        # 通过 game_id 查找游戏名称，使用 get_game_type_from_room 作为后备
        case get_game_by_id(current_room.game_id) do
          {:ok, game} -> game.name
          _ -> get_game_type_from_room(current_room)
        end

      true ->
        "unknown"
    end
  end

  # 通过 ID 获取游戏信息
  defp get_game_by_id(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_id(game_id) do
        {:ok, game} -> {:ok, game}
        {:error, _} -> {:error, :not_found}
      end
    rescue
      _ -> {:error, :query_failed}
    end
  end

  # 获取 Slot777 默认基础配置
  defp get_slot777_default_basic_config do
    # 直接返回真正的默认基础配置，不从数据库获取
    %{
      "room_id" => "4001",
      "room_name" => "Slot777 房间",
      "room_type" => "slot777",
      "max_players" => 100,
      "min_bet" => 1,
      "max_bet" => 1000,
      "default_bet" => 10,
      "bet_levels" => [1, 5, 10, 25, 50, 100, 250, 500, 1000],
      "auto_spin_enabled" => true,
      "turbo_mode_enabled" => true,
      "sound_enabled" => true,
      "animation_speed" => "normal",
      "game_settings" => %{
        "rows" => 3,
        "cols" => 5,
        "symbols" => [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        "wild_symbol" => 11,
        "scatter_symbol" => 12,
        "paylines" => [
          [1, 1, 1, 1, 1],
          [0, 0, 0, 0, 0],
          [2, 2, 2, 2, 2],
          [0, 1, 2, 1, 0],
          [2, 1, 0, 1, 2],
          [1, 0, 0, 0, 1],
          [1, 2, 2, 2, 1],
          [0, 0, 1, 2, 2],
          [2, 2, 1, 0, 0],
          [1, 0, 1, 2, 1],
          [1, 2, 1, 0, 1],
          [0, 1, 0, 1, 0],
          [2, 1, 2, 1, 2],
          [0, 1, 1, 1, 0],
          [2, 1, 1, 1, 2],
          [1, 1, 0, 1, 1],
          [1, 1, 2, 1, 1],
          [0, 0, 2, 0, 0],
          [2, 2, 0, 2, 2],
          [0, 2, 0, 2, 0]
        ]
      }
    }
  end

  # 获取 Slot777 默认玩法配置
  defp get_slot777_default_gameplay_config do
    # 直接返回真正的默认玩法配置，不从数据库获取
    %{
      "rtp" => 95.0,
      "icon_weights" => %{
        "1" => 100,
        "2" => 90,
        "3" => 80,
        "4" => 70,
        "5" => 60,
        "6" => 50,
        "7" => 40,
        "8" => 30,
        "9" => 20,
        "10" => 15,
        "11" => 10,
        "12" => 5
      },
      "payout_table" => %{
        "1" => %{"3" => 5, "4" => 10, "5" => 25},
        "2" => %{"3" => 5, "4" => 10, "5" => 25},
        "3" => %{"3" => 10, "4" => 20, "5" => 50},
        "4" => %{"3" => 10, "4" => 20, "5" => 50},
        "5" => %{"3" => 15, "4" => 30, "5" => 75},
        "6" => %{"3" => 15, "4" => 30, "5" => 75},
        "7" => %{"3" => 20, "4" => 40, "5" => 100},
        "8" => %{"3" => 25, "4" => 50, "5" => 125},
        "9" => %{"3" => 30, "4" => 60, "5" => 150},
        "10" => %{"3" => 50, "4" => 100, "5" => 250},
        "11" => %{"2" => 2, "3" => 10, "4" => 50, "5" => 500},
        "12" => %{"2" => 1, "3" => 5, "4" => 25, "5" => 100}
      },
      "jackpot_config" => %{
        "enabled" => true,
        "seed_amount" => 1000,
        "contribution_rate" => 0.01,
        "trigger_probability" => 0.001,
        "max_amount" => 100_000
      },
      "free_game_config" => %{
        "enabled" => true,
        "trigger_symbols" => [12],
        "min_trigger_count" => 3,
        "free_spins_count" => 10,
        "multiplier" => 2
      },
      "testing_config" => %{
        "force_win" => false,
        "force_symbols" => [],
        "debug_mode" => false
      },
      "big_win_control" => %{
        "enabled" => true,
        "threshold_multiplier" => 10,
        "max_frequency" => 0.05,
        "cooldown_spins" => 100
      }
    }
  end

  # 获取默认基础配置 JSON 字符串
  defp get_default_basic_config_json do
    Jason.encode!(get_default_basic_config(), pretty: true)
  end

  # 获取默认玩法配置 JSON 字符串
  defp get_default_gameplay_config_json do
    Jason.encode!(get_default_gameplay_config(), pretty: true)
  end
end
