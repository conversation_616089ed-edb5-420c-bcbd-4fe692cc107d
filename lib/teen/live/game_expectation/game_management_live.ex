defmodule Teen.Live.GameExpectation.GameManagementLive do
  @moduledoc """
  游戏管理后台界面 - 基于 Backpex 框架
  提供游戏配置的增删改查、状态管理等功能
  """
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.GameManagement.ManageGameConfig,
    ],
    layout: {Teen.Layouts, :admin}

  use CypridinaWeb, :verified_routes

  require Ash.Query
  import Phoenix.LiveView, only: [put_flash: 3, push_navigate: 2]
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  # Backpex 必需的回调函数
  @impl Backpex.LiveResource
  def singular_name, do: "游戏"

  @impl Backpex.LiveResource
  def plural_name, do: "游戏管理"

  @impl Backpex.LiveResource
  def fields do
    [
      game_id: %{
        module: Backpex.Fields.Number,
        label: "游戏ID",
        searchable: true,
        help_text: "唯一标识符，用于系统内部识别"
      },
      game_name: %{
        module: Backpex.Fields.Text,
        label: "游戏名称",
        searchable: true,
        help_text: "系统内部使用的游戏名称"
      },
      display_name: %{
        module: Backpex.Fields.Text,
        label: "显示名称",
        searchable: true,
        help_text: "用户看到的游戏名称"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "游戏描述",
        help_text: "游戏的详细说明（可选）"
      },
      icon_url: %{
        module: Backpex.Fields.URL,
        label: "图标URL",
        help_text: "游戏图标的网络地址（可选）"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "运行状态",
        options: [
          {"🟢 正常运行", 2},
          {"🟡 维护中", 1},
          {"🔴 已停用", 0},
          {"🔵 即将开放", 4}
        ],
        render: fn
          %{value: 2} = assigns ->
            ~H"""
            <div class="badge badge-success">🟢 正常运行</div>
            """
          %{value: 1} = assigns ->
            ~H"""
            <div class="badge badge-warning">🟡 维护中</div>
            """
          %{value: 0} = assigns ->
            ~H"""
            <div class="badge badge-error">🔴 已停用</div>
            """
          %{value: 4} = assigns ->
            ~H"""
            <div class="badge badge-info">🔵 即将开放</div>
            """
          assigns ->
            ~H"""
            <div class="badge badge-neutral">未知状态</div>
            """
        end
      },
      is_enabled: %{
        module: Backpex.Fields.Boolean,
        label: "启用状态",
        help_text: "启用后用户可以访问此游戏",
        render: fn
          %{value: true} = assigns ->
            ~H"""
            <div class="badge badge-success badge-outline">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              启用
            </div>
            """
          %{value: false} = assigns ->
            ~H"""
            <div class="badge badge-error badge-outline">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              禁用
            </div>
            """
        end
      },
      display_order: %{
        module: Backpex.Fields.Number,
        label: "显示顺序",
        help_text: "数字越小排序越靠前"
      }
    ]
  end

  # 筛选器配置
  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Backpex.Filters.Select,
        label: "运行状态",
        options: [
          {"全部状态", nil},
          {"正常运行", 2},
          {"维护中", 1},
          {"已停用", 0},
          {"即将开放", 4}
        ]
      },
      is_enabled: %{
        module: Backpex.Filters.Boolean,
        label: "启用状态"
      }
    ]
  end

  # 自定义操作
  @impl Backpex.LiveResource
  def actions do
    [
      toggle_status: %{
        module: Backpex.Actions.Item,
        label: "切换状态",
        icon: "hero-power",
        confirm: "确定要切换游戏状态吗？",
        handler: &toggle_game_status/2
      },
      manage_rooms: %{
        module: Backpex.Actions.Item,
        label: "房间配置",
        icon: "hero-building-office",
        handler: &manage_rooms/2
      }
    ]
  end

  # Backpex 操作处理函数
  defp toggle_game_status(socket, item) do
    try do
      updated_item =
        item
        |> Ash.Changeset.for_update(:update, %{is_enabled: !item.is_enabled})
        |> Ash.update!()

      socket = put_flash(socket, :info, "游戏状态已更新")
      {:ok, socket, updated_item}
    rescue
      error ->
        require Logger
        Logger.error("切换游戏状态失败: #{inspect(error)}")
        socket = put_flash(socket, :error, "切换状态失败")
        {:error, socket}
    end
  end

  defp manage_rooms(socket, item) do
    socket = push_navigate(socket, to: ~p"/admin/rooms?game_id=#{item.game_id}")
    {:ok, socket, item}
  end

  # 自定义验证函数
  @impl Backpex.LiveResource
  def can?(assigns, action, item \\ nil)

  def can?(_assigns, :index, _item), do: true
  def can?(_assigns, :show, _item), do: true
  def can?(_assigns, :new, _item), do: true
  def can?(_assigns, :edit, _item), do: true
  def can?(_assigns, :delete, _item), do: true
  def can?(_assigns, :toggle_status, _item), do: true
  def can?(_assigns, :manage_rooms, _item), do: true
end
