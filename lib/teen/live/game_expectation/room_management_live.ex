defmodule Teen.Live.GameExpectation.RoomManagementLive do
  @moduledoc """
  房间管理后台界面 - 基于 Backpex 框架
  提供游戏房间配置的增删改查功能
  """
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.GameManagement.LeveRoomConfig,
    ],
    layout: {Teen.Layouts, :admin}

  require Ash.Query
  import Phoenix.LiveView, only: [put_flash: 3]
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  # Backpex 必需的回调函数
  @impl Backpex.LiveResource
  def singular_name, do: "房间"

  @impl Backpex.LiveResource
  def plural_name, do: "房间管理"

  @impl Backpex.LiveResource
  def fields do
    [
      room_id: %{
        module: Backpex.Fields.Number,
        label: "房间ID",
        searchable: true,
        help_text: "房间的唯一标识符"
      },
      room_name: %{
        module: Backpex.Fields.Text,
        label: "房间名称",
        searchable: true,
        help_text: "房间的显示名称"
      },
      game_id: %{
        module: Backpex.Fields.Number,
        label: "游戏ID",
        help_text: "所属游戏的ID"
      },
      level: %{
        module: Backpex.Fields.Number,
        label: "房间等级",
        help_text: "房间的等级设置"
      },
      min_score: %{
        module: Backpex.Fields.Number,
        label: "最低积分",
        help_text: "进入房间所需的最低积分"
      },
      max_score: %{
        module: Backpex.Fields.Number,
        label: "最高积分",
        help_text: "进入房间允许的最高积分"
      },
      base_score: %{
        module: Backpex.Fields.Number,
        label: "基础积分",
        help_text: "房间的基础积分设置"
      },
      basic_config: %{
        module: Backpex.Fields.Textarea,
        label: "基础配置",
        help_text: "房间的基础配置JSON"
      },
      gameplay_config: %{
        module: Backpex.Fields.Textarea,
        label: "游戏配置",
        help_text: "房间的游戏玩法配置JSON"
      },
      is_enabled: %{
        module: Backpex.Fields.Boolean,
        label: "启用状态",
        help_text: "房间是否启用"
      }
    ]
  end

  # 筛选器配置
  @impl Backpex.LiveResource
  def filters do
    [
      game_id: %{
        module: Backpex.Filters.Select,
        label: "所属游戏",
        options: fn ->
          ManageGameConfig
          |> Ash.read!()
          |> Enum.map(&{&1.display_name, &1.game_id})
        end
      },
      is_enabled: %{
        module: Backpex.Filters.Boolean,
        label: "启用状态"
      },
      level: %{
        module: Backpex.Filters.Number,
        label: "房间等级"
      }
    ]
  end

  # 自定义操作
  @impl Backpex.LiveResource
  def actions do
    [
      toggle_status: %{
        module: Backpex.Actions.Item,
        label: "切换状态",
        icon: "hero-power",
        confirm: "确定要切换房间状态吗？",
        handler: &toggle_room_status/2
      }
    ]
  end

  # Backpex 操作处理函数
  defp toggle_room_status(socket, item) do
    try do
      updated_item =
        item
        |> Ash.Changeset.for_update(:update, %{is_enabled: !item.is_enabled})
        |> Ash.update!()

      socket = put_flash(socket, :info, "房间状态已更新")
      {:ok, socket, updated_item}
    rescue
      error ->
        require Logger
        Logger.error("切换房间状态失败: #{inspect(error)}")
        socket = put_flash(socket, :error, "切换状态失败")
        {:error, socket}
    end
  end

  # 自定义验证函数
  @impl Backpex.LiveResource
  def can?(assigns, action, item \\ nil)

  def can?(_assigns, :index, _item), do: true
  def can?(_assigns, :show, _item), do: true
  def can?(_assigns, :new, _item), do: true
  def can?(_assigns, :edit, _item), do: true
  def can?(_assigns, :delete, _item), do: true
  def can?(_assigns, :toggle_status, _item), do: true

  # 自定义查询
  @impl Backpex.LiveResource
  def query(query, _live_action, _assigns) do
    query
    |> Ash.Query.sort([:game_id, :level, :room_id])
  end
end
