defmodule Teen.Live.GameExpectation.GameManagementBackpexLive do
  @moduledoc """
  游戏管理后台界面 - Backpex 版本
  使用 Backpex 框架提供现代化的游戏配置管理界面
  """
  use Backpex.LiveResource,
    layout: {CypridinaWeb.Layouts, :admin},
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.GameManagement.ManageGameConfig
    ]

  use CypridinaWeb, :verified_routes

  require Ash.Query
  import Phoenix.LiveView, only: [put_flash: 3, push_navigate: 2]
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  # Backpex 配置
  @impl Backpex.LiveResource
  def singular_name, do: "游戏"

  @impl Backpex.LiveResource
  def plural_name, do: "游戏管理"

  # 字段配置
  @impl Backpex.LiveResource
  def fields do
    [
      game_id: %{
        module: Backpex.Fields.Text,
        label: "游戏ID",
        searchable: true,
        orderable: true
      },
      game_name: %{
        module: Backpex.Fields.Text,
        label: "游戏名称",
        searchable: true,
        orderable: true
      },
      display_name: %{
        module: Backpex.Fields.Text,
        label: "显示名称",
        searchable: true,
        orderable: true
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "游戏描述",
        searchable: true
      },
      icon_url: %{
        module: Backpex.Fields.URL,
        label: "图标URL"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "运行状态",
        options: [
          {0, "已停用"},
          {1, "维护中"},
          {2, "正常运行"},
          {4, "即将开放"}
        ],
        render: fn
          %{status: 0} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">已停用</span>))
          %{status: 1} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">维护中</span>))
          %{status: 2} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">正常运行</span>))
          %{status: 4} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">即将开放</span>))
          _ ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">未知</span>))
        end
      },
      is_enabled: %{
        module: Backpex.Fields.Boolean,
        label: "启用状态",
        render: fn
          %{is_enabled: true} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">已启用</span>))
          %{is_enabled: false} ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">已禁用</span>))
          _ ->
            Phoenix.HTML.raw(~s(<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">未知</span>))
        end
      },
      display_order: %{
        module: Backpex.Fields.Number,
        label: "显示顺序",
        orderable: true
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        orderable: true,
        format: "%Y-%m-%d %H:%M:%S"
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        orderable: true,
        format: "%Y-%m-%d %H:%M:%S"
      }
    ]
  end

  # 筛选器配置
  @impl Backpex.LiveResource
  def filters do
    []
  end

  # 自定义操作
  @impl Backpex.LiveResource
  def item_actions(_assigns) do
    [
      toggle_status: %{
        module: Backpex.ItemActions.Custom,
        label: "切换状态",
        icon: "hero-power",
        confirm: "确定要切换游戏状态吗？",
        handler: &toggle_game_status/2
      },
      manage_rooms: %{
        module: Backpex.ItemActions.Custom,
        label: "房间配置",
        icon: "hero-building-office",
        handler: &manage_rooms/2
      }
    ]
  end

  # 资源操作
  @impl Backpex.LiveResource
  def resource_actions do
    [
      sync_games: %{
        module: Backpex.ResourceActions.Custom,
        label: "同步游戏数据",
        icon: "hero-arrow-path",
        confirm: "确定要同步游戏数据吗？",
        handler: &sync_games/1
      }
    ]
  end

  # 面板配置
  @impl Backpex.LiveResource
  def panels do
    [
      stats: %{
        module: Backpex.Panels.Custom,
        label: "游戏统计",
        render: &render_stats_panel/1
      }
    ]
  end

  # 指标配置
  @impl Backpex.LiveResource
  def metrics do
    [
      total_games: %{
        module: Backpex.Metrics.Value,
        label: "游戏总数",
        value: fn -> get_total_games_count() end
      },
      active_games: %{
        module: Backpex.Metrics.Value,
        label: "活跃游戏",
        value: fn -> get_active_games_count() end
      },
      enabled_games: %{
        module: Backpex.Metrics.Value,
        label: "已启用游戏",
        value: fn -> get_enabled_games_count() end
      }
    ]
  end

  # 自定义验证函数
  @impl Backpex.LiveResource
  def can?(assigns, action, item \\ nil)

  def can?(_assigns, :index, _item), do: true
  def can?(_assigns, :show, _item), do: true
  def can?(_assigns, :new, _item), do: true
  def can?(_assigns, :edit, _item), do: true
  def can?(_assigns, :delete, _item), do: true
  def can?(_assigns, :toggle_status, _item), do: true
  def can?(_assigns, :manage_rooms, _item), do: true
  def can?(_assigns, :sync_games, _item), do: true

  # 操作处理函数
  defp toggle_game_status(socket, item) do
    try do
      updated_item =
        item
        |> Ash.Changeset.for_update(:update, %{is_enabled: !item.is_enabled})
        |> Ash.update!()

      socket = put_flash(socket, :info, "游戏状态已更新")
      {:ok, socket, updated_item}
    rescue
      error ->
        require Logger
        Logger.error("切换游戏状态失败: #{inspect(error)}")
        socket = put_flash(socket, :error, "切换状态失败")
        {:error, socket}
    end
  end

  defp manage_rooms(socket, item) do
    socket = push_navigate(socket, to: ~p"/admin/games/#{item.game_id}/rooms")
    {:ok, socket, item}
  end

  defp sync_games(socket) do
    try do
      # 这里应该实现同步游戏数据的逻辑
      socket = put_flash(socket, :info, "游戏数据同步成功")
      {:ok, socket}
    rescue
      error ->
        require Logger
        Logger.error("同步游戏数据失败: #{inspect(error)}")
        socket = put_flash(socket, :error, "同步数据失败")
        {:error, socket}
    end
  end

  # 统计面板渲染
  defp render_stats_panel(_assigns) do
    stats = get_game_stats()

    Phoenix.HTML.raw("""
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">游戏总数</dt>
                <dd class="text-lg font-medium text-gray-900">#{stats.total}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">活跃游戏</dt>
                <dd class="text-lg font-medium text-gray-900">#{stats.active}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">已启用</dt>
                <dd class="text-lg font-medium text-gray-900">#{stats.enabled}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">已禁用</dt>
                <dd class="text-lg font-medium text-gray-900">#{stats.disabled}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
    """)
  end

  # 统计数据获取函数
  defp get_total_games_count do
    ManageGameConfig
    |> Ash.Query.count()
    |> Ash.read!()
  end

  defp get_active_games_count do
    ManageGameConfig
    |> Ash.Query.filter(status == 2)
    |> Ash.Query.count()
    |> Ash.read!()
  end

  defp get_enabled_games_count do
    ManageGameConfig
    |> Ash.Query.filter(is_enabled == true)
    |> Ash.Query.count()
    |> Ash.read!()
  end

  defp get_game_stats do
    total = get_total_games_count()
    active = get_active_games_count()
    enabled = get_enabled_games_count()

    %{
      total: total,
      active: active,
      enabled: enabled,
      disabled: total - enabled
    }
  end
end
