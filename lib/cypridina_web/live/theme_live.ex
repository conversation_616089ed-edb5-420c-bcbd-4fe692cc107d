defmodule CypridinaWeb.ThemeLive do
  @moduledoc """
  主题管理和预览页面
  """
  
  use CypridinaWeb, :live_view
  alias CypridinaWeb.Components.ThemeSwitcher

  @impl true
  def mount(_params, session, socket) do
    current_theme = get_current_theme(session)
    
    socket = 
      socket
      |> assign(:current_theme, current_theme)
      |> assign(:themes, ThemeSwitcher.get_available_themes())
      |> assign(:preview_theme, current_theme)
      |> assign(:show_preview, false)
      |> assign(:page_title, "主题管理")

    {:ok, socket}
  end

  @impl true
  def handle_event("change_theme", %{"theme" => theme}, socket) do
    if ThemeSwitcher.theme_exists?(theme) do
      # 更新当前主题
      socket = 
        socket
        |> assign(:current_theme, theme)
        |> put_flash(:info, "主题已切换到 #{get_theme_label(theme)}")
      
      # 通过 JavaScript 更新页面主题
      {:noreply, push_event(socket, "theme-changed", %{theme: theme})}
    else
      {:noreply, put_flash(socket, :error, "无效的主题")}
    end
  end

  @impl true
  def handle_event("toggle_theme", _params, socket) do
    new_theme = if socket.assigns.current_theme == "light", do: "dark", else: "light"
    
    socket = 
      socket
      |> assign(:current_theme, new_theme)
      |> put_flash(:info, "已切换到 #{get_theme_label(new_theme)} 主题")
    
    {:noreply, push_event(socket, "theme-changed", %{theme: new_theme})}
  end

  @impl true
  def handle_event("preview_theme", %{"theme" => theme}, socket) do
    socket = 
      socket
      |> assign(:preview_theme, theme)
      |> assign(:show_preview, true)
    
    {:noreply, push_event(socket, "preview-theme", %{theme: theme})}
  end

  @impl true
  def handle_event("apply_preview", _params, socket) do
    preview_theme = socket.assigns.preview_theme
    
    socket = 
      socket
      |> assign(:current_theme, preview_theme)
      |> assign(:show_preview, false)
      |> put_flash(:info, "已应用 #{get_theme_label(preview_theme)} 主题")
    
    {:noreply, push_event(socket, "theme-changed", %{theme: preview_theme})}
  end

  @impl true
  def handle_event("cancel_preview", _params, socket) do
    current_theme = socket.assigns.current_theme
    
    socket = 
      socket
      |> assign(:show_preview, false)
      |> assign(:preview_theme, current_theme)
    
    {:noreply, push_event(socket, "theme-changed", %{theme: current_theme})}
  end

  @impl true
  def handle_event("reset_theme", _params, socket) do
    default_theme = "light"
    
    socket = 
      socket
      |> assign(:current_theme, default_theme)
      |> assign(:preview_theme, default_theme)
      |> assign(:show_preview, false)
      |> put_flash(:info, "已重置为默认主题")
    
    {:noreply, push_event(socket, "theme-changed", %{theme: default_theme})}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-base-100 transition-all duration-300">
      <!-- 页面头部 -->
      <div class="bg-base-200 border-b border-base-300">
        <div class="container mx-auto px-4 py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-base-content">主题管理</h1>
              <p class="text-base-content/70 mt-1">个性化您的界面体验</p>
            </div>
            
            <div class="flex items-center gap-4">
              <!-- 当前主题显示 -->
              <div class="bg-base-100 rounded-lg px-4 py-2 border border-base-300">
                <div class="flex items-center gap-2">
                  <span class="text-lg"><%= get_theme_icon(@current_theme) %></span>
                  <div>
                    <div class="text-sm font-medium text-base-content">当前主题</div>
                    <div class="text-xs text-base-content/70"><%= get_theme_label(@current_theme) %></div>
                  </div>
                </div>
              </div>
              
              <!-- 主题切换器 -->
              <ThemeSwitcher.theme_switcher current_theme={@current_theme} show_label={true} />
            </div>
          </div>
        </div>
      </div>

      <!-- 预览模式提示 -->
      <%= if @show_preview do %>
        <div class="bg-warning/10 border-b border-warning/20">
          <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <.icon name="hero-eye" class="w-5 h-5 text-warning" />
                <span class="text-warning font-medium">
                  正在预览 <%= get_theme_label(@preview_theme) %> 主题
                </span>
              </div>
              <div class="flex items-center gap-2">
                <button 
                  class="btn btn-sm btn-success"
                  phx-click="apply_preview"
                >
                  应用主题
                </button>
                <button 
                  class="btn btn-sm btn-ghost"
                  phx-click="cancel_preview"
                >
                  取消预览
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 主要内容 -->
      <div class="container mx-auto px-4 py-8">
        <!-- 快速操作 -->
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-base-content">快速操作</h2>
            <button 
              class="btn btn-outline btn-sm"
              phx-click="reset_theme"
            >
              <.icon name="hero-arrow-path" class="w-4 h-4" />
              重置为默认
            </button>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button 
              class="btn btn-outline"
              phx-click="toggle_theme"
            >
              <.icon name="hero-sun" class="w-5 h-5" />
              切换明暗
            </button>
            <button 
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="modern-blue"
            >
              💙 现代蓝
            </button>
            <button 
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="nature-green"
            >
              🌿 自然绿
            </button>
            <button 
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="warm-orange"
            >
              🧡 温暖橙
            </button>
          </div>
        </div>

        <!-- 主题网格 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-base-content mb-4">所有主题</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <%= for theme <- @themes do %>
              <ThemeSwitcher.theme_preview_card 
                theme={theme} 
                current_theme={@current_theme}
                class="animate-fade-in-up"
              />
            <% end %>
          </div>
        </div>

        <!-- 主题统计 -->
        <div class="bg-base-200 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-base-content mb-4">主题统计</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="stat">
              <div class="stat-title">总主题数</div>
              <div class="stat-value text-primary"><%= length(@themes) %></div>
            </div>
            <div class="stat">
              <div class="stat-title">浅色主题</div>
              <div class="stat-value text-secondary">
                <%= Enum.count(@themes, &(not String.contains?(&1.name, "dark"))) %>
              </div>
            </div>
            <div class="stat">
              <div class="stat-title">深色主题</div>
              <div class="stat-value text-accent">
                <%= Enum.count(@themes, &String.contains?(&1.name, "dark")) %>
              </div>
            </div>
            <div class="stat">
              <div class="stat-title">当前主题</div>
              <div class="stat-value text-info"><%= get_theme_label(@current_theme) %></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 私有函数

  defp get_current_theme(session) do
    session["theme"] || "light"
  end

  defp get_theme_icon(theme_name) do
    case ThemeSwitcher.get_theme_info(theme_name) do
      %{icon: icon} -> icon
      _ -> "🎨"
    end
  end

  defp get_theme_label(theme_name) do
    case ThemeSwitcher.get_theme_info(theme_name) do
      %{label: label} -> label
      _ -> theme_name
    end
  end
end
